Write-Host "Python 3.9.13 indiriliyor ve kuruluyor..."

$pythonUrl = "https://www.python.org/ftp/python/3.9.13/python-3.9.13-amd64.exe"
$installerPath = "$env:TEMP\python-3.9.13-amd64.exe"

try {
    # Python installer'ı indir
    Write-Host "Python installer indiriliyor..."
    Invoke-WebRequest -Uri $pythonUrl -OutFile $installerPath -UseBasicParsing

    if (Test-Path $installerPath) {
        Write-Host "İndirme tamamlandı. Kurulum başlatılıyor..."

        # Sessiz kurulum parametreleri
        $installArgs = "/quiet InstallAllUsers=1 PrependPath=1 Include_launcher=1"

        $process = Start-Process -FilePath $installerPath -ArgumentList $installArgs -Wait -PassThru

        if ($process.ExitCode -eq 0) {
            Write-Host "Python 3.9.13 kurulumu başar<PERSON><PERSON> tamamlandı!"
            # Installer dosyasını sil
            Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
            Write-Host "Kurulum tamamlandı. Lütfen PowerShell'i kapatıp yeniden açın."
            Pause
        } else {
            Write-Host "HATA: Kurulum sırasında bir hata oluştu! Çıkış kodu: $($process.ExitCode)"
            Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
            exit 1
        }
    } else {
        Write-Host "HATA: Installer dosyası indirilemedi!"
        exit 1
    }
}
catch {
    Write-Host "HATA: Python kurulumu sırasında bir hata oluştu:"
    Write-Host $_.Exception.Message
    if (Test-Path $installerPath) {
        Remove-Item $installerPath -Force -ErrorAction SilentlyContinue
    }
    exit 1
}