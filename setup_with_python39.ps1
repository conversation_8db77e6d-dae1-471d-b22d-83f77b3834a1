# Sanal ortam klasör adini belirle
$VENV_DIR = "venv"

# Python 3.9 kontrolu (py launcher ile)
function Check-Python39 {
    try {
        $python39VersionOutput = py -3.9 --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Host "Python 3.9 bulundu: $python39VersionOutput"
            return $true
        } else {
            Write-Host "Python 3.9 bulunamadi."
            return $false
        }
    } catch {
        Write-Host "py launcher veya Python 3.9 bulunamadi."
        return $false
    }
}

function Install-Python39 {
    Write-Host "Python 3.9 yukleniyor..."
    & "$PSScriptRoot\install_python39.ps1"

    # Kurulumdan sonra tekrar kontrol et
    $python39VersionOutput = py -3.9 --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Python 3.9 başariyla kuruldu: $python39VersionOutput"
        return $true
    } else {
        Write-Host "HATA: Python 3.9 kurulumu başarisiz!"
        Pause
        exit 1
    }
}

# Ana akiş
if (-not (Check-Python39)) {
    if (-not (Install-Python39)) {
        Write-Host "HATA: Python 3.9 kurulumu başarisiz!"
        Pause
        exit 1
    }
}

# Python 3.9 path'i
$python39Path = "py -3.9"

# Sanal ortam olustur (Python 3.9 ile)
if (-not (Test-Path $VENV_DIR)) {
    Write-Host "Sanal ortam olusturuluyor (Python 3.9 ile)..."
    & py -3.9 -m venv $VENV_DIR
    if ($LASTEXITCODE -ne 0) {
        Write-Host "HATA: Sanal ortam oluşturulamadi!"
        Pause
        exit 1
    }
} else {
    Write-Host "Sanal ortam zaten var: $VENV_DIR"
}

# Ortami aktive et
$activateScript = Join-Path $VENV_DIR "Scripts\Activate.ps1"
if (Test-Path $activateScript) {
    & $activateScript
    Write-Host "Sanal ortam aktif edildi."
} else {
    Write-Host "Aktivasyon dosyasi bulunamadi: $activateScript"
    Pause
    exit 1
}

# Ana bagimliliklari yukle
if (Test-Path "requirements.txt") {
    Write-Host "requirements.txt yukleniyor..."
    pip install -r requirements.txt
    if ($LASTEXITCODE -ne 0) {
        Write-Host "UYARI: requirements.txt yuklenirken hata oluştu."
    }
} else {
    Write-Host "requirements.txt bulunamadi."
}

# Gelistirme bagimliliklari istege bagli
if (Test-Path "requirements-dev.txt") {
    $DEVINSTALL = Read-Host "Gelistirme bagimliliklari yuklensin mi? (E/H)"
    if ($DEVINSTALL -ieq "E") {
        pip install -r requirements-dev.txt
        if ($LASTEXITCODE -ne 0) {
            Write-Host "UYARI: requirements-dev.txt yuklenirken hata oluştu."
        }
    } else {
        Write-Host "Gelistirme bagimliliklari atlandi."
    }
} else {
    Write-Host "requirements-dev.txt bulunamadi."
}

Write-Host ""
Write-Host "Ortam hazir! Python 3.9 ile sanal ortam aktif."
Write-Host ""
Write-Host "Komut satirinda isiniz bitince 'deactivate' yazabilirsiniz."
Pause