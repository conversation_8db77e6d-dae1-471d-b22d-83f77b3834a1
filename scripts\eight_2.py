#!/usr/bin/env python3
"""
8 Şekli Çizme Görevi - Sabit Kanatlı Uçak için (Python 3.13 Uyumlu)
Bu script MAVProxy'yi ba<PERSON>r, uçağı uçurur ve belirlenen direkler etrafında 8 şekli çizdirir.
"""
import os
import sys
import time
import math
import argparse
import subprocess
from pymavlink import mavutil
from pymavlink.mavutil import mavlink

# Python 3.13 uyumluluğu için dronekit yerine pymavlink kullanacağız
class LocationGlobalRelative:
    """Dronekit LocationGlobalRelative sınıfının basit bir alternatifi"""
    def __init__(self, lat, lon, alt):
        self.lat = lat
        self.lon = lon
        self.alt = alt

class LocationGlobal:
    """Dronekit LocationGlobal sınıfının basit bir alternatifi"""
    def __init__(self, lat, lon, alt):
        self.lat = lat
        self.lon = lon
        self.alt = alt

class VehicleMode:
    """Dronekit VehicleMode sınıfının basit bir alternatifi"""
    def __init__(self, mode):
        self.name = mode

class Command:
    """Dronekit Command sınıfının basit bir alternatifi"""
    def __init__(self, target_system, target_component, seq, frame, command, current, autocontinue, 
                 param1, param2, param3, param4, x, y, z):
        self.target_system = target_system
        self.target_component = target_component
        self.seq = seq
        self.frame = frame
        self.command = command
        self.current = current
        self.autocontinue = autocontinue
        self.param1 = param1
        self.param2 = param2
        self.param3 = param3
        self.param4 = param4
        self.x = x
        self.y = y
        self.z = z

class Vehicle:
    """Dronekit Vehicle sınıfının basit bir alternatifi"""
    def __init__(self, connection_string):
        self.master = mavutil.mavlink_connection(connection_string)
        self.location = type('obj', (object,), {
            'global_frame': LocationGlobal(0, 0, 0),
            'global_relative_frame': LocationGlobalRelative(0, 0, 0)
        })()
        self.commands = type('obj', (object,), {
            'next': 0,
            'clear': self._clear_commands,
            'add': self._add_command,
            'upload': self._upload_commands
        })()
        self._commands_list = []
        self.armed = False
        self.mode = VehicleMode("STABILIZE")
        self.is_armable = False
        self._update_location()
        self._check_armable()
    
    def _update_location(self):
        """Konum bilgilerini güncelle"""
        try:
            msg = self.master.recv_match(type='GLOBAL_POSITION_INT', blocking=False)
            if msg:
                self.location.global_frame.lat = msg.lat / 1e7
                self.location.global_frame.lon = msg.lon / 1e7
                self.location.global_frame.alt = msg.alt / 1000.0
                self.location.global_relative_frame.lat = msg.lat / 1e7
                self.location.global_relative_frame.lon = msg.lon / 1e7
                self.location.global_relative_frame.alt = msg.relative_alt / 1000.0
        except:
            pass
    
    def _check_armable(self):
        """Arm edilebilirlik durumunu kontrol et"""
        try:
            # SYS_STATUS mesajını kontrol et
            msg = self.master.recv_match(type='SYS_STATUS', blocking=False)
            if msg:
                # Sensör durumlarını kontrol et
                sensors_ok = (msg.onboard_control_sensors_health &
                             mavlink.MAV_SYS_STATUS_SENSOR_GPS) != 0
                self.is_armable = sensors_ok

            # HEARTBEAT mesajını da kontrol et
            heartbeat = self.master.recv_match(type='HEARTBEAT', blocking=False)
            if heartbeat:
                # Sistem durumu kontrolü
                system_ok = heartbeat.system_status in [
                    mavlink.MAV_STATE_STANDBY,
                    mavlink.MAV_STATE_ACTIVE
                ]
                self.is_armable = self.is_armable and system_ok
        except:
            pass
    
    def _clear_commands(self):
        """Komutları temizle"""
        self._commands_list = []
    
    def _add_command(self, cmd):
        """Komut ekle"""
        self._commands_list.append(cmd)
    
    def _upload_commands(self):
        """Komutları yükle"""
        # MAVLink mission upload implementasyonu
        self.master.mav.mission_clear_all_send(self.master.target_system, self.master.target_component)
        time.sleep(0.5)
        
        # Mission count gönder
        self.master.mav.mission_count_send(self.master.target_system, self.master.target_component, len(self._commands_list))
        
        # Her komut için mission item gönder
        for i, cmd in enumerate(self._commands_list):
            self.master.mav.mission_item_send(
                self.master.target_system,
                self.master.target_component,
                i,
                cmd.frame,
                cmd.command,
                cmd.current,
                cmd.autocontinue,
                cmd.param1,
                cmd.param2,
                cmd.param3,
                cmd.param4,
                cmd.x,
                cmd.y,
                cmd.z
            )
            time.sleep(0.1)
        
        print(f"{len(self._commands_list)} komut yüklendi")
    
    def close(self):
        """Bağlantıyı kapat"""
        self.master.close()

def connect(connection_string, wait_ready=True):
    """Dronekit connect fonksiyonunun alternatifi"""
    vehicle = Vehicle(connection_string)
    if wait_ready:
        time.sleep(2)  # Bağlantı için bekle
    return vehicle

# Argüman parser'ı oluştur
parser = argparse.ArgumentParser(description='8 şekli çizme görevi - Sabit Kanatlı Uçak (Python 3.13)')
parser.add_argument('--connect', default='tcp:127.0.0.1:5762', help='Bağlantı dizesi')
parser.add_argument('--mavproxy', action='store_true', help='MAVProxy başlat')
parser.add_argument('--altitude', type=float, default=100.0, help='Uçuş yüksekliği (metre)')
parser.add_argument('--radius', type=float, default=80.0, help='Dönüş yarıçapı (metre)')
args = parser.parse_args()

# MAVProxy'yi başlat (eğer istenirse)
mavproxy_process = None
if args.mavproxy:
    print("MAVProxy başlatılıyor...")
    cmd = ['mavproxy.py', '--master=' + args.connect, '--console', '--map']
    mavproxy_process = subprocess.Popen(cmd)
    time.sleep(5)  # MAVProxy'nin başlaması için bekle

# Araca bağlan
print(f"Araca bağlanılıyor: {args.connect}")
vehicle = connect(args.connect, wait_ready=True)

def get_distance_metres(aLocation1, aLocation2):
    """İki konum arasındaki mesafeyi metre cinsinden hesaplar"""
    dlat = aLocation2.lat - aLocation1.lat
    dlong = aLocation2.lon - aLocation1.lon
    return math.sqrt((dlat*dlat) + (dlong*dlong)) * 1.113195e5

def create_eight_pattern(center_lat, center_lon, radius, altitude):
    """İki direk etrafında 8 şekli oluşturan waypoint'leri hesaplar"""
    waypoints = []
    
    # Daha fazla waypoint ile daha yumuşak dönüşler
    num_points = 12  # Her daire için nokta sayısı
    
    # İlk direk etrafında daire (saat yönünde)
    for i in range(num_points):
        angle = math.radians(i * (360/num_points))
        lat = center_lat + (radius * math.cos(angle) * 1e-5)
        lon = center_lon + (radius * math.sin(angle) * 1e-5)
        waypoints.append(LocationGlobalRelative(lat, lon, altitude))
    
    # İkinci direk etrafında daire (saat yönünün tersine)
    for i in range(num_points):
        angle = math.radians(180 + i * (360/num_points))
        lat = center_lat + (radius * math.cos(angle) * 1e-5)
        lon = center_lon + (radius * math.sin(angle) * 1e-5)
        waypoints.append(LocationGlobalRelative(lat, lon, altitude))
    
    return waypoints

def upload_mission(waypoints):
    """Waypoint'leri araca yükler"""
    cmds = vehicle.commands
    cmds.clear()
    
    # Takeoff komutu ekle - Sabit kanatlı uçak için
    cmds.add(Command(0, 0, 0, mavlink.MAV_FRAME_GLOBAL_RELATIVE_ALT, mavlink.MAV_CMD_NAV_TAKEOFF, 
                    0, 0, 0, 0, 0, 0, 0, 0, args.altitude))
    
    # Waypoint'leri ekle
    for i, waypoint in enumerate(waypoints):
        # Sabit kanatlı uçak için daha yumuşak dönüşler
        cmds.add(Command(0, 0, 0, mavlink.MAV_FRAME_GLOBAL_RELATIVE_ALT, mavlink.MAV_CMD_NAV_WAYPOINT, 
                        0, 0, 2, 15, 0, 0, waypoint.lat, waypoint.lon, waypoint.alt))
    
    # RTL (Return To Launch) komutu ekle
    cmds.add(Command(0, 0, 0, mavlink.MAV_FRAME_GLOBAL_RELATIVE_ALT, mavlink.MAV_CMD_NAV_RETURN_TO_LAUNCH, 
                    0, 0, 0, 0, 0, 0, 0, 0, 0))
    
    # Komutları yükle
    cmds.upload()
    print("Görev yüklendi.")

def arm_and_takeoff_plane(target_altitude):
    """Sabit kanatlı uçağı arm eder ve RC3 ile kalkış yapar"""
    print("Temel kontroller yapılıyor...")

    # Arm edilebilir durumda olana kadar bekle
    timeout = 30
    start_time = time.time()
    while (time.time() - start_time) < timeout:
        vehicle._check_armable()
        if vehicle.is_armable:
            break
        print("Arm için bekleniyor...")
        time.sleep(1)

    if not vehicle.is_armable:
        print("UYARI: Uçak arm edilebilir durumda değil!")
        return False

    print("MANUAL moduna geçiliyor...")
    # Önce MANUAL moda geç
    vehicle.master.mav.set_mode_send(
        vehicle.master.target_system,
        mavlink.MAV_MODE_FLAG_CUSTOM_MODE_ENABLED,
        0  # MANUAL mode
    )
    vehicle.mode = VehicleMode("MANUAL")
    time.sleep(2)

    print("Arm ediliyor...")
    # Arm komutu gönder
    vehicle.master.mav.command_long_send(
        vehicle.master.target_system,
        vehicle.master.target_component,
        mavlink.MAV_CMD_COMPONENT_ARM_DISARM,
        0,
        1, 0, 0, 0, 0, 0, 0
    )

    # Arm olana kadar bekle
    timeout = 30
    start_time = time.time()
    armed_confirmed = False
    while (time.time() - start_time) < timeout:
        print("Arm olması bekleniyor...")
        # Heartbeat mesajını kontrol et
        msg = vehicle.master.recv_match(type='HEARTBEAT', blocking=False)
        if msg and msg.base_mode & mavlink.MAV_MODE_FLAG_SAFETY_ARMED:
            vehicle.armed = True
            armed_confirmed = True
            break
        time.sleep(1)

    if not armed_confirmed:
        print("UYARI: Arm işlemi zaman aşımına uğradı!")
        return False
    else:
        print("Uçak arm edildi.")

    # RC3 (throttle) ile kalkış yap
    print("RC3 throttle ile kalkış yapılıyor...")
    print("Throttle yavaşça artırılıyor...")

    # Throttle'ı yavaşça artır (1000'den 1800'e)
    for throttle_value in range(1000, 1801, 50):
        print(f"Throttle: {throttle_value}")
        # RC override komutu gönder (kanal 3 = throttle)
        vehicle.master.mav.rc_channels_override_send(
            vehicle.master.target_system,
            vehicle.master.target_component,
            65535,  # chan1_raw (roll) - no override
            65535,  # chan2_raw (pitch) - no override
            throttle_value,  # chan3_raw (throttle)
            65535,  # chan4_raw (yaw) - no override
            65535,  # chan5_raw - no override
            65535,  # chan6_raw - no override
            65535,  # chan7_raw - no override
            65535   # chan8_raw - no override
        )
        time.sleep(0.5)

        # Yükseklik kontrolü
        vehicle._update_location()
        current_altitude = vehicle.location.global_relative_frame.alt
        print(f"Yükseklik: {current_altitude:.2f}m")

        # Eğer uçak havaya kalktıysa (5m üzeri) throttle'ı sabit tut
        if current_altitude > 5.0:
            print("Uçak havaya kalktı! Throttle sabitlendi.")
            break

    # Hedef yüksekliğe ulaşana kadar throttle'ı ayarla
    print(f"Hedef yükseklik {target_altitude}m'ye çıkılıyor...")
    timeout = 120  # 2 dakika timeout
    start_time = time.time()

    while (time.time() - start_time) < timeout:
        vehicle._update_location()
        current_altitude = vehicle.location.global_relative_frame.alt
        print(f"Yükseklik: {current_altitude:.2f}m / Hedef: {target_altitude}m")

        # Yükseklik kontrolü ile throttle ayarı
        if current_altitude < target_altitude * 0.8:
            # Daha fazla yükseklik gerekli - throttle artır
            throttle_value = 1700
        elif current_altitude < target_altitude * 0.95:
            # Hedefe yakın - orta throttle
            throttle_value = 1500
        else:
            # Hedef yüksekliğe ulaşıldı
            throttle_value = 1400
            print("Hedef yüksekliğe ulaşıldı!")
            break

        # Throttle komutunu gönder
        vehicle.master.mav.rc_channels_override_send(
            vehicle.master.target_system,
            vehicle.master.target_component,
            65535, 65535, throttle_value, 65535, 65535, 65535, 65535, 65535
        )

        time.sleep(2)

    # AUTO moduna geç
    print("AUTO moduna geçiliyor...")
    vehicle.master.mav.set_mode_send(
        vehicle.master.target_system,
        mavlink.MAV_MODE_FLAG_CUSTOM_MODE_ENABLED,
        3  # AUTO mode
    )
    vehicle.mode = VehicleMode("AUTO")

    # RC override'ı kaldır (autopilot'a kontrolü ver)
    vehicle.master.mav.rc_channels_override_send(
        vehicle.master.target_system,
        vehicle.master.target_component,
        65535, 65535, 65535, 65535, 65535, 65535, 65535, 65535
    )
    print("RC override kaldırıldı, autopilot kontrolü aldı.")

    return True

def main():
    try:
        # Direk konumlarını sahada gir
        print("\n--- 8 Şekli Çizme Görevi (Sabit Kanatlı Uçak - Python 3.13) ---")
        print("Direk konumlarını girin:")

        # Test için varsayılan değerler kullanabilirsiniz
        use_default = input("Varsayılan test konumlarını kullanmak ister misiniz? (e/h): ")

        if use_default.lower() == 'e':
            # Ev konumunu merkez al ve direkler için offset kullan
            vehicle._update_location()
            home_location = vehicle.location.global_frame
            pole1_lat = home_location.lat + 0.001  # Yaklaşık 100m kuzey
            pole1_lon = home_location.lon
            pole2_lat = home_location.lat - 0.001  # Yaklaşık 100m güney
            pole2_lon = home_location.lon
            print(f"Direk 1: {pole1_lat}, {pole1_lon}")
            print(f"Direk 2: {pole2_lat}, {pole2_lon}")
        else:
            pole1_lat = float(input("1. Direğin enlem değeri: "))
            pole1_lon = float(input("1. Direğin boylam değeri: "))
            pole2_lat = float(input("2. Direğin enlem değeri: "))
            pole2_lon = float(input("2. Direğin boylam değeri: "))

        # Uçuş parametreleri
        altitude = args.altitude  # metre
        radius = args.radius  # metre

        # Her direk için 8 şekli oluştur
        print("8 şekli rotası oluşturuluyor...")
        waypoints1 = create_eight_pattern(pole1_lat, pole1_lon, radius, altitude)
        waypoints2 = create_eight_pattern(pole2_lat, pole2_lon, radius, altitude)

        # Tüm waypoint'leri birleştir
        all_waypoints = waypoints1 + waypoints2

        # Görevi yükle
        print("Görev yükleniyor...")
        upload_mission(all_waypoints)

        # Kalkış yap - Sabit kanatlı uçak için
        print("Kalkış yapılıyor...")
        takeoff_success = arm_and_takeoff_plane(altitude)

        if not takeoff_success:
            print("Kalkış başarısız! Program sonlandırılıyor.")
            return

        # Görev başlatılmış olmalı (AUTO modunda)
        print("Görev başlatıldı - 8 şekli çiziliyor...")

        # Görev tamamlanana kadar bekle
        timeout = 600  # 10 dakika timeout
        start_time = time.time()
        while (time.time() - start_time) < timeout:
            # Mission current mesajını kontrol et
            msg = vehicle.master.recv_match(type='MISSION_CURRENT', blocking=False)
            if msg:
                vehicle.commands.next = msg.seq
                nextwaypoint = vehicle.commands.next
                print(f"Sonraki waypoint: {nextwaypoint}")

                # Görev tamamlandı mı kontrol et
                if nextwaypoint >= len(all_waypoints) + 2:  # +2: takeoff ve RTL komutları
                    print("Görev tamamlandı!")
                    break

            time.sleep(2)

        # RTL moduna geç
        print("Eve dönülüyor...")
        vehicle.master.mav.set_mode_send(
            vehicle.master.target_system,
            mavlink.MAV_MODE_FLAG_CUSTOM_MODE_ENABLED,
            6  # RTL mode
        )
        vehicle.mode = VehicleMode("RTL")

        # İniş tamamlanana kadar bekle
        timeout = 300  # 5 dakika timeout
        start_time = time.time()
        while vehicle.armed and (time.time() - start_time) < timeout:
            print("İniş bekleniyor...")
            # Heartbeat mesajını kontrol et
            msg = vehicle.master.recv_match(type='HEARTBEAT', blocking=False)
            if msg and not (msg.base_mode & mavlink.MAV_MODE_FLAG_SAFETY_ARMED):
                vehicle.armed = False
            time.sleep(2)

        print("İniş tamamlandı!")

    except KeyboardInterrupt:
        print("\nKullanıcı tarafından durduruldu.")
    except Exception as e:
        print(f"Hata oluştu: {e}")
    finally:
        # Bağlantıyı kapat
        print("Bağlantı kapatılıyor...")
        vehicle.close()

        # MAVProxy'yi kapat (eğer başlatıldıysa)
        if mavproxy_process:
            mavproxy_process.terminate()

        print("Program sonlandırıldı.")

if __name__ == "__main__":
    main()
